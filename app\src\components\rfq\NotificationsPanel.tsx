import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useRFQStore } from "@/store/rfqStore";

export function NotificationsPanel() {
  const notifications = useRFQStore(s => s.notifications);
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notifications</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {notifications.length === 0 && <p className="text-sm text-muted-foreground">No notifications</p>}
        {notifications.map(n => (
          <div key={n.id} className="border rounded p-2">
            <div className="text-sm font-medium">{n.title}</div>
            {n.message && <div className="text-sm text-muted-foreground">{n.message}</div>}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

