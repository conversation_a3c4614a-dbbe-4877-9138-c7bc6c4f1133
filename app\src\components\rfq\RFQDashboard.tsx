import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useRFQStore } from "@/store/rfqStore";
import { RFQDetailCard } from "./RFQDetailCard";
import { StatusBadge } from "./StatusBadge";
import { VendorEditorDialog } from "./VendorEditorDialog";
import { DevVendorDebugPanel } from "./DevVendorDebugPanel";

export function RFQDashboard() {
  const rfqs = useRFQStore(s => s.rfqs);
  const vendors = useRFQStore(s => s.vendors);
  const createRFQ = useRFQStore(s => s.createRFQ);
  const [activeId, setActiveId] = React.useState<string | null>(null);

  function handleCreate() {
    const rfq = createRFQ({
      id: undefined as any,
      refNumber: `Q-${Math.floor(Math.random()*10000)}`,
      title: "New RFQ",
      description: "",
      status: "Draft" as any,
      client: { company: "" },
      createdAt: new Date().toISOString(),
      dueDate: undefined,
      responseDeadline: undefined,
      notes: "",
      attachments: [],
      lineItems: [],
      invitedVendorIds: [],
      history: [] as any,
    } as any);
    setActiveId(rfq.id);
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-1 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>RFQ Dashboard</CardTitle>
            <CardDescription>Create, manage and track RFQs</CardDescription>
import { VendorEditorDialog } from "./VendorEditorDialog";
import { DevVendorDebugPanel } from "./DevVendorDebugPanel";
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex gap-2">
              <Button onClick={handleCreate}>Create New RFQ</Button>
              <Input placeholder="Search" />
              <VendorEditorDialog />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>List</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {rfqs.length === 0 && <p className="text-sm text-muted-foreground">No RFQs yet</p>}
            {rfqs.map(r => (
              <button key={r.id} onClick={() => setActiveId(r.id)} className={`w-full text-left p-3 border rounded hover:bg-muted ${activeId===r.id? 'bg-muted': ''}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{r.title}</div>
                    <div className="text-xs text-muted-foreground">{r.refNumber} • {new Date(r.createdAt).toLocaleDateString()}</div>
                  </div>
                  <StatusBadge status={r.status} />
                </div>
              </button>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className="lg:col-span-2 space-y-4">
        {activeId ? <RFQDetailCard id={activeId} /> : (
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Select an RFQ to view details.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

import * as React from "react";

