import React, { useState, useMemo } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useRFQStore } from "@/store/rfqStore";
import { ActionButton } from "./ActionButton";
import { Award, TrendingDown, TrendingUp, AlertTriangle, Sparkles, Download } from "lucide-react";

interface ProposalAnalysis {
  bestPrice: string;
  worstPrice: string;
  averagePrice: number;
  priceRange: number;
  recommendations: string[];
  riskFactors: string[];
}

export function ProposalComparisonTable({ rfqId }: { rfqId: string }) {
  const rfq = useRFQStore(s => s.rfqs.find(r => r.id === rfqId));
  const quotes = useRFQStore(s => s.quotes.filter(q => q.rfqId === rfqId));
  const vendors = useRFQStore(s => s.vendors);
  const awardQuote = useRFQStore(s => s.awardQuote);
  const rejectQuote = useRFQStore(s => s.rejectQuote);

  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<ProposalAnalysis | null>(null);

  if (!rfq) return null;

  // Collect item ids to ensure matrix alignment
  const itemIds = rfq.lineItems.map(li => li.id);

  // Calculate proposal statistics
  const proposalStats = useMemo(() => {
    if (quotes.length === 0) return null;

    const totals = quotes.map(q => q.total);
    const minPrice = Math.min(...totals);
    const maxPrice = Math.max(...totals);
    const avgPrice = totals.reduce((a, b) => a + b, 0) / totals.length;

    const bestQuote = quotes.find(q => q.total === minPrice);
    const worstQuote = quotes.find(q => q.total === maxPrice);

    return {
      minPrice,
      maxPrice,
      avgPrice,
      bestVendor: bestQuote ? vendors.find(v => v.id === bestQuote.vendorId)?.name : 'Unknown',
      worstVendor: worstQuote ? vendors.find(v => v.id === worstQuote.vendorId)?.name : 'Unknown',
      priceRange: maxPrice - minPrice,
      savings: maxPrice - minPrice
    };
  }, [quotes, vendors]);

  const generateAIAnalysis = async () => {
    if (quotes.length === 0) return;

    setIsAnalyzing(true);
    try {
      const proposalData = quotes.map(q => {
        const vendor = vendors.find(v => v.id === q.vendorId);
        return {
          vendor: vendor?.name || 'Unknown',
          total: q.total,
          currency: q.currency,
          deliveryDate: q.deliveryDate,
          deliveryTerms: q.deliveryTerms,
          notes: q.notes
        };
      });

      const prompt = `Analyze these RFQ proposals and provide insights:
      RFQ: ${rfq.title}
      Proposals: ${JSON.stringify(proposalData, null, 2)}

      Provide analysis on best value, risks, and recommendations.`;

      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: prompt })
      });

      const result = await response.json();

      // Parse AI response into structured analysis
      const analysis: ProposalAnalysis = {
        bestPrice: proposalStats?.bestVendor || 'Unknown',
        worstPrice: proposalStats?.worstVendor || 'Unknown',
        averagePrice: proposalStats?.avgPrice || 0,
        priceRange: proposalStats?.priceRange || 0,
        recommendations: [
          result.recommendations || 'AI analysis completed',
          'Consider delivery terms and vendor reliability',
          'Verify compliance requirements'
        ],
        riskFactors: [
          'Price variations detected',
          'Delivery timeline differences',
          'Vendor capability assessment needed'
        ]
      };

      setAiAnalysis(analysis);
    } catch (error) {
      console.error('AI analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAward = (quoteId: string) => {
    awardQuote(quoteId);
  };

  const handleReject = (quoteId: string) => {
    rejectQuote(quoteId);
  };

  const exportComparison = () => {
    // Generate CSV or PDF export
    const csvContent = [
      ['Vendor', 'Total', 'Currency', 'Delivery', 'Status'],
      ...quotes.map(q => {
        const vendor = vendors.find(v => v.id === q.vendorId);
        return [
          vendor?.name || 'Unknown',
          q.total.toString(),
          q.currency,
          q.deliveryDate || q.deliveryTerms || '-',
          q.status || 'Pending'
        ];
      })
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rfq-${rfq.refNumber}-comparison.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6")
    <div className="border rounded-md overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Vendor</TableHead>
            {itemIds.map(id => (
              <TableHead key={id}>{rfq.lineItems.find(li => li.id === id)?.itemName || 'Item'}</TableHead>
            ))}
            <TableHead>Total</TableHead>
            <TableHead>Delivery</TableHead>
            <TableHead>Notes</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {quotes.map(q => {
            const awarded = q.status === 'Awarded';
            return (
              <TableRow key={q.id} className={awarded ? 'bg-green-50/10' : ''}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {(() => { const v = vendors.find(v => v.id === q.vendorId); return (
                      <>
                        <span className="font-medium">{v?.name || q.vendorId}</span>
                        {v?.email && <a className="text-primary hover:underline" href={`mailto:${v.email}`} onClick={(e)=>e.stopPropagation()}>Email</a>}
                        {v?.phone && <a className="text-primary hover:underline" href={`tel:${v.phone}`} onClick={(e)=>e.stopPropagation()}>Call</a>}
                      </>
                    ); })()}
                    {awarded && <Badge>AWARDED</Badge>}
                  </div>
                </TableCell>
                {itemIds.map(id => {
                  const item = q.items.find(i => i.lineItemId === id);
                  return (
                    <TableCell key={id}>{item ? `${item.unitPrice} ${item.currency}` : '-'}</TableCell>
                  );
                })}
                <TableCell>{q.total} {q.currency}</TableCell>
                <TableCell>{q.deliveryDate?.slice(0,10) || q.deliveryTerms || '-'}</TableCell>
                <TableCell className="max-w-[200px] truncate">{q.notes || '-'}</TableCell>
              </TableRow>
            );
          })}
          {quotes.length === 0 && (
            <TableRow>
              <TableCell colSpan={3 + itemIds.length} className="text-center text-sm text-muted-foreground">No proposals yet</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

