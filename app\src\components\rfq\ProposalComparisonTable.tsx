import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useRFQStore } from "@/store/rfqStore";
import { Badge } from "@/components/ui/badge";

export function ProposalComparisonTable({ rfqId }: { rfqId: string }) {
  const rfq = useRFQStore(s => s.rfqs.find(r => r.id === rfqId));
  const quotes = useRFQStore(s => s.quotes.filter(q => q.rfqId === rfqId));
  const vendors = useRFQStore(s => s.vendors);
  if (!rfq) return null;

  // Collect item ids to ensure matrix alignment
  const itemIds = rfq.lineItems.map(li => li.id);

  return (
    <div className="border rounded-md overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Vendor</TableHead>
            {itemIds.map(id => (
              <TableHead key={id}>{rfq.lineItems.find(li => li.id === id)?.itemName || 'Item'}</TableHead>
            ))}
            <TableHead>Total</TableHead>
            <TableHead>Delivery</TableHead>
            <TableHead>Notes</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {quotes.map(q => {
            const awarded = q.status === 'Awarded';
            return (
              <TableRow key={q.id} className={awarded ? 'bg-green-50/10' : ''}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {(() => { const v = vendors.find(v => v.id === q.vendorId); return (
                      <>
                        <span className="font-medium">{v?.name || q.vendorId}</span>
                        {v?.email && <a className="text-primary hover:underline" href={`mailto:${v.email}`} onClick={(e)=>e.stopPropagation()}>Email</a>}
                        {v?.phone && <a className="text-primary hover:underline" href={`tel:${v.phone}`} onClick={(e)=>e.stopPropagation()}>Call</a>}
                      </>
                    ); })()}
                    {awarded && <Badge>AWARDED</Badge>}
                  </div>
                </TableCell>
                {itemIds.map(id => {
                  const item = q.items.find(i => i.lineItemId === id);
                  return (
                    <TableCell key={id}>{item ? `${item.unitPrice} ${item.currency}` : '-'}</TableCell>
                  );
                })}
                <TableCell>{q.total} {q.currency}</TableCell>
                <TableCell>{q.deliveryDate?.slice(0,10) || q.deliveryTerms || '-'}</TableCell>
                <TableCell className="max-w-[200px] truncate">{q.notes || '-'}</TableCell>
              </TableRow>
            );
          })}
          {quotes.length === 0 && (
            <TableRow>
              <TableCell colSpan={3 + itemIds.length} className="text-center text-sm text-muted-foreground">No proposals yet</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

