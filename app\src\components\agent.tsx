'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import ReactMarkdown from 'react-markdown';
import { Send, Search, FileText, TrendingUp, Users, ShoppingCart, AlertCircle, CheckCircle } from 'lucide-react';
import { P2POverviewCard } from "@/components/p2p/P2POverviewCard";
import { RequisitionForm } from "@/components/p2p/RequisitionForm";
import { ApprovalDashboard } from "@/components/p2p/ApprovalDashboard";
import { RFQModule } from "@/components/p2p/RFQModule";
import { POWorkflow } from "@/components/p2p/POWorkflow";
import { GoodsReceipt } from "@/components/p2p/GoodsReceipt";
import { InvoiceMatching } from "@/components/p2p/InvoiceMatching";
import { Payments } from "@/components/p2p/Payments";
import { RFQDashboard } from "@/components/rfq/RFQDashboard";
import { NotificationsPanel } from "@/components/rfq/NotificationsPanel";
import { useRFQStore } from "@/store/rfqStore";
import type { PurchaseRequisition } from "@/types/p2p";

export function Agent() {
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [activeTab, setActiveTab] = useState('chat');
  const ingestAI = useRFQStore(s => s.ingestAIResponse);
  const loadAll = useRFQStore(s => s.loadAll);

  useEffect(() => {
    loadAll?.();
  }, [loadAll]);
  // Local demo state for P2P flows
  const [requisitions, setRequisitions] = useState<PurchaseRequisition[]>([]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    setIsLoading(true);

    try {
      const apiResponse = await fetch('/api/langbase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: input.trim() })
      });

      // Parse the response first
      let data;
      try {
        const contentType = apiResponse.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          data = await apiResponse.json();
        } else {
          const textData = await apiResponse.text();
          // Try to parse as JSON in case content-type header is missing
          try {
            data = JSON.parse(textData);
          } catch {
            data = textData;
          }
        }
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Check for errors after parsing
      if (!apiResponse.ok) {
        let errorMessage = `HTTP ${apiResponse.status}: ${apiResponse.statusText}`;

        if (data && typeof data === 'object') {
          errorMessage = data.error || data.message || errorMessage;
        } else if (typeof data === 'string') {
          errorMessage = data || errorMessage;
        }

        throw new Error(errorMessage);
      }

      // Check for application-level errors
      if (data && typeof data === 'object' && (data.success === false || data.error)) {
        const errorMessage = data.error || data.message || 'An error occurred while processing your request';
        throw new Error(errorMessage);
      }

      // Handle successful response
      if (data && typeof data === 'object' && (data.type === 'rfq' || data.type === 'report' || data.type === 'proposal')) {
        // Route AI structured outputs to RFQ UI store
        ingestAI({ type: data.type, rfq: data.rfq, report: data.report, proposal: data.proposal, context: data.context });
      } else {
        // Attempt a backend transform to coerce into structured payload
        try {
          const tx = await fetch('/api/transform/agent-output', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) });
          const shaped = await tx.json();
          if (shaped && typeof shaped === 'object' && (shaped.type === 'rfq' || shaped.type === 'report' || shaped.type === 'proposal')) {
            ingestAI({ type: shaped.type, rfq: shaped.rfq, report: shaped.report, proposal: shaped.proposal, context: shaped.context });
          } else {
            // Fall back to showing raw
            if (typeof data === 'string') setResponse({ output: data });
            else if (data && typeof data === 'object') setResponse(data);
            else setResponse({ output: String(data) });
          }
        } catch {
          if (typeof data === 'string') setResponse({ output: data });
          else if (data && typeof data === 'object') setResponse(data);
          else setResponse({ output: String(data) });
        }
      }

      toast.success('Procurement analysis completed successfully!', {
        closeButton: true,
        duration: 3000
      });

      setInput('');
      setActiveTab('results');

    } catch (error) {
      console.error('Error processing request:', error);

      // Extract meaningful error message
      let errorMessage = 'An error occurred while processing your request';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.toString && error.toString() !== '[object Object]') {
        errorMessage = error.toString();
      }

      toast.error(errorMessage, {
        closeButton: true,
        duration: Infinity
      });
    } finally {
      setIsLoading(false);
    }
  };

  const quickActions = [
    { label: 'Find suppliers in Mangaluru', icon: Search, query: 'Find eco-friendly packaging suppliers in Mangaluru, India under $10,000 budget' },
    { label: 'Generate RFQ template', icon: FileText, query: 'Generate an RFQ template for office furniture procurement with sustainability requirements' },
    { label: 'Analyze spend patterns', icon: TrendingUp, query: 'Analyze our current procurement spend patterns and suggest cost-saving opportunities' },
    { label: 'Supplier performance review', icon: Users, query: 'Review supplier performance metrics and recommend improvements for our top 5 vendors' }
  ];

  const handleQuickAction = (query) => {
    setInput(query);
  };

  const renderResponse = () => {
    if (!response) return null;

    try {
      // Handle different response formats
      if (typeof response === 'string') {
        return (
          <div className="prose prose-invert max-w-none">
            <ReactMarkdown>{response}</ReactMarkdown>
          </div>
        );
      }

      // Handle fallback response
      if (response.fallback_response) {
        return (
          <Card className="bg-card/50 border-yellow-500/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-yellow-500">
                <AlertCircle className="h-5 w-5" />
                Limited Analysis Available
              </CardTitle>
              <CardDescription>
                Analysis completed with basic recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-invert max-w-none">
                <ReactMarkdown>{response.fallback_response}</ReactMarkdown>
              </div>
            </CardContent>
          </Card>
        );
      }

      if (response.output) {
        return (
          <div className="prose prose-invert max-w-none">
            <ReactMarkdown>{response.output}</ReactMarkdown>
          </div>
        );
      }

      // Handle structured workflow response
      if (response.query_analysis || response.recommendations || response.action_plan) {
        return (
          <div className="space-y-6">
            {response.query_analysis && (
              <Card className="bg-card/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    Query Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    <ReactMarkdown>{response.query_analysis}</ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {response.market_intelligence && (
              <Card className="bg-card/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Market Intelligence
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    <ReactMarkdown>{response.market_intelligence}</ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {response.recommendations && (
              <Card className="bg-card/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    <ReactMarkdown>{response.recommendations}</ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {response.documents && (
              <Card className="bg-card/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Generated Documents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    <ReactMarkdown>{response.documents}</ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {response.action_plan && (
              <Card className="bg-card/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Action Plan
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-invert max-w-none">
                    <ReactMarkdown>{response.action_plan}</ReactMarkdown>
                  </div>
                </CardContent>
              </Card>
            )}

            {response.region && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Badge variant="outline">{response.region}</Badge>
                {response.timestamp && (
                  <span>Generated: {new Date(response.timestamp).toLocaleString()}</span>
                )}
              </div>
            )}
          </div>
        );
      }

      // Fallback for any other response format
      return (
        <div className="prose prose-invert max-w-none">
          <ReactMarkdown>{JSON.stringify(response, null, 2)}</ReactMarkdown>
        </div>
      );
    } catch (renderError) {
      console.error('Error rendering response:', renderError);
      return (
        <Card className="bg-card/50 border-red-500/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-500">
              <AlertCircle className="h-5 w-5" />
              Display Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              There was an error displaying the response. Raw data:
            </p>
            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
              {JSON.stringify(response, null, 2)}
            </pre>
          </CardContent>
        </Card>
      );
    }
  };

  return (
    <div className="min-h-screen bg-background text-foreground p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl md:text-4xl font-bold">Procurement AI Agent</h1>
          <p className="text-muted-foreground text-lg">
            Intelligent B2B procurement automation for Mangaluru and global markets
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="results" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Results
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="p2p" className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4" />
              P2P
            </TabsTrigger>
            <TabsTrigger value="rfq" className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4" />
              RFQ
            </TabsTrigger>

          </TabsList>

          <TabsContent value="chat" className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Get started with common procurement tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="h-auto p-4 justify-start text-left"
                      onClick={() => handleQuickAction(action.query)}
                    >
                      <action.icon className="h-5 w-5 mr-3 flex-shrink-0" />
                      <span>{action.label}</span>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Chat Interface */}
            <Card>
              <CardHeader>
                <CardTitle>Procurement Assistant</CardTitle>
                <CardDescription>
                  Ask me anything about procurement, suppliers, market analysis, or document generation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <Textarea
                    placeholder="e.g., Find sustainable packaging suppliers in Mangaluru under $5,000 budget with ESG compliance..."
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    className="min-h-[120px] resize-none"
                    disabled={isLoading}
                  />
                  <Button
                    type="submit"
                    disabled={isLoading || !input.trim()}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Analyze Procurement Request
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="results" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Procurement Analysis Results</CardTitle>
                <CardDescription>
                  Comprehensive analysis and recommendations for your procurement needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-auto max-h-[800px]">
                  {response ? (
                    renderResponse()
                  ) : (
                    <div className="text-center text-muted-foreground py-12">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No analysis results yet. Submit a procurement request to get started.</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>


          <TabsContent value="p2p" className="space-y-6">
            <P2POverviewCard stage="Requisition" />
            <RequisitionForm onSubmit={(req) => setRequisitions((prev) => [req, ...prev])} />
            <ApprovalDashboard
              items={requisitions.filter((r) => r.status === "submitted" || r.status === "pending_approval")}
              onDecision={(id, decision) =>
                setRequisitions((prev) =>
                  prev.map((r) => (r.id === id ? { ...r, status: decision } : r))
                )
              }
            />
          </TabsContent>

          <TabsContent value="rfq" className="space-y-6">
            <P2POverviewCard stage="Sourcing" />
            <RFQDashboard />
            <NotificationsPanel />
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-6">
            {/* P2P Overview Quick Card (compact) */}
            <P2POverviewCard stage="Requisition" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Suppliers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">247</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Monthly Spend</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹45.2L</div>
                  <p className="text-xs text-muted-foreground">
                    -8% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹8.7L</div>
                  <p className="text-xs text-muted-foreground">
                    +23% from last month
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Procurement Activities</CardTitle>
                <CardDescription>
                  Latest procurement requests and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { item: 'Office Furniture RFQ', status: 'Completed', amount: '₹2.3L', supplier: 'Mangaluru Furniture Co.' },
                    { item: 'IT Equipment Purchase', status: 'In Progress', amount: '₹5.8L', supplier: 'Tech Solutions Ltd.' },
                    { item: 'Packaging Materials', status: 'Pending Approval', amount: '₹1.2L', supplier: 'EcoPackage India' },
                    { item: 'Cleaning Supplies', status: 'Completed', amount: '₹45K', supplier: 'Green Clean Services' }
                  ].map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <p className="font-medium">{activity.item}</p>
                        <p className="text-sm text-muted-foreground">{activity.supplier}</p>
                      </div>
                      <div className="text-right space-y-1">
                        <p className="font-medium">{activity.amount}</p>
                        <Badge variant={
                          activity.status === 'Completed' ? 'default' :
                          activity.status === 'In Progress' ? 'secondary' : 'outline'
                        }>
                          {activity.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}