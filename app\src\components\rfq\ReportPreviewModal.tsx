import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Download, Edit, Send, FileText, Building, Calendar, User, DollarSign, Sparkles } from "lucide-react";
import { useRFQStore } from "@/store/rfqStore";
import type { RFQ, Quote } from "@/store/rfqStore";

interface ReportPreviewModalProps {
  rfq: RFQ;
  isOpen: boolean;
  onClose: () => void;
  reportType: 'rfq' | 'comparison' | 'award';
}

export function ReportPreviewModal({ rfq, isOpen, onClose, reportType }: ReportPreviewModalProps) {
  const quotes = useRFQStore(s => s.quotes.filter(q => q.rfqId === rfq.id));
  const vendors = useRFQStore(s => s.vendors);
  const reports = useRFQStore(s => s.reports.filter(r => r.rfqId === rfq.id));
  const [isEditing, setIsEditing] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  const report = reports[0];

  const generateAIReport = async () => {
    setIsGeneratingAI(true);
    try {
      const prompt = `Generate a professional ${reportType} report for RFQ ${rfq.refNumber}: ${rfq.title}.
      Include executive summary, detailed analysis, and recommendations.`;

      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input: prompt })
      });

      const result = await response.json();
      // In a real implementation, this would update the report in the store
      console.log('AI Report generated:', result);
    } catch (error) {
      console.error('AI report generation failed:', error);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button variant="outline" onClick={() => setOpen(true)}>Preview Report</Button>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{report?.title || 'Report Preview'}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {report ? (
            report.sections.map((s, idx) => (
              <section key={idx} className="space-y-2">
                <h3 className="text-lg font-semibold">{s.heading}</h3>
                {s.markdown && <pre className="whitespace-pre-wrap text-sm">{s.markdown}</pre>}
                {s.html && <div dangerouslySetInnerHTML={{ __html: s.html }} />}
              </section>
            ))
          ) : (
            <p className="text-sm text-muted-foreground">No AI report available yet.</p>
          )}
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="ghost" onClick={() => window.print?.()}>Download PDF</Button>
            <Button onClick={() => setOpen(false)}>Send</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

