import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useRFQStore } from "@/store/rfqStore";
import { useState } from "react";

export function ReportPreviewModal({ rfqId }: { rfqId: string }) {
  const reports = useRFQStore(s => s.reports.filter(r => r.rfqId === rfqId));
  const [open, setOpen] = useState(false);
  const report = reports[0];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button variant="outline" onClick={() => setOpen(true)}>Preview Report</Button>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{report?.title || 'Report Preview'}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {report ? (
            report.sections.map((s, idx) => (
              <section key={idx} className="space-y-2">
                <h3 className="text-lg font-semibold">{s.heading}</h3>
                {s.markdown && <pre className="whitespace-pre-wrap text-sm">{s.markdown}</pre>}
                {s.html && <div dangerouslySetInnerHTML={{ __html: s.html }} />}
              </section>
            ))
          ) : (
            <p className="text-sm text-muted-foreground">No AI report available yet.</p>
          )}
          <div className="flex justify-end gap-2 pt-2">
            <Button variant="ghost" onClick={() => window.print?.()}>Download PDF</Button>
            <Button onClick={() => setOpen(false)}>Send</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

