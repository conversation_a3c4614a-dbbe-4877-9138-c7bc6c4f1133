import { Context, Hono } from "hono";
import dotenv from "dotenv";
dotenv.config();
import { Langbase } from "langbase";


// Basic API endpoint
export const registerLangbaseEndpoint = (app: Hono) => {
  // Existing route used by current frontend
  app.post("/api/langbase", async (c: Context) => {
    const request = new Request(c.req.url, {
      method: c.req.method,
      headers: {
        "Content-Type": c.req.header("Content-Type") || "application/json",
        Authorization: c.req.header("Authorization") || "",
      },
      body: JSON.stringify(await c.req.json()),
    });
    return handleAgentRequest(request);
  });

  // New alias route to match "/api/agent" (Express-style sample)
  app.post("/api/agent", async (c: Context) => {
    const request = new Request(c.req.url, {
      method: c.req.method,
      headers: {
        "Content-Type": c.req.header("Content-Type") || "application/json",
      },
      body: JSON.stringify(await c.req.json()),
    });
    return handleAgentRequest(request);
  });
};

// Server-side only: Do NOT include it in any client-side code, that ends up in the browsers.

function isDegraded(data: any): boolean {
  try {
    const s = JSON.stringify(data || {}).toLowerCase();
    return (
      s.includes('limited analysis available') ||
      s.includes('please check your api keys') ||
      (typeof data === 'object' && data && (data.status === 'error' || data.message === 'Procurement analysis completed with limited data'))
    );
  } catch {
    return false;
  }
}

async function runHosted(input: string) {
  const apiKey = process.env.LANGBASE_API_KEY;
  if (!apiKey) {
    return { ok: false, status: 500, data: { error: 'LANGBASE_API_KEY is not set. Add it to app/.env.' } };
  }
  const apiUrl = process.env.LANGBASE_API_URL || 'https://api.langbase.com/aymnsh174883/perplex-procure-agent-4b33';
  try {
    const resp = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${apiKey}` },
      body: JSON.stringify({ input })
    });
    const data = await resp.json().catch(() => ({}));
    return { ok: resp.ok, status: resp.status, data };
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    return { ok: false, status: 500, data: { error: message } };
  }
}

async function runLocalWorkflow(input: string) {
  const langbase = new Langbase({ apiKey: process.env.LANGBASE_API_KEY! });
  const workflow = langbase.workflow();
  const { step } = workflow;
  try {
    const queryAnalysis = await step({ id: 'analyze_query', run: async () => {
      const { output } = await langbase.agent.run({
        model: 'perplexity:sonar-pro', apiKey: process.env.PERPLEXITY_API_KEY!,
        instructions: 'Analyze the procurement query and produce structured parameters.',
        input: [{ role: 'user', content: input }], stream: false,
      });
      return output;
    }});

    const relevantMemories = await step({ id: 'retrieve_data', run: async () => {
      try {
        return await langbase.memories.retrieve({
          query: input,
          memory: [
            { name: 'procurement-policies-1753937590380' },
            { name: 'supplier-database-2rhhmmzx' },
            { name: 'contract-templates-97ndpm2m' },
            { name: 'compliance-docs-xiauwdun' }
          ],
        });
      } catch {
        return [{ text: 'Fallback procurement policies: Standard approval workflows, ESG compliance required, regional supplier preferences for Mangaluru market.' }];
      }
    }});

    const marketResearch = await step({ id: 'market_research', run: async () => {
      const { output } = await langbase.agent.run({
        model: 'perplexity:sonar-pro', apiKey: process.env.PERPLEXITY_API_KEY!,
        instructions: 'Search for current market prices, suppliers, compliance and ESG signals.',
        input: [{ role: 'user', content: `Research market data for: ${input}` }], stream: false,
      });
      return output;
    }});

    const recommendations = await step({ id: 'recommendations', run: async () => {
      const context = `Analysis: ${queryAnalysis}\n\nKnowledge: ${(relevantMemories||[]).map((m:any)=>m.text).join('\n')}\n\nMarket: ${marketResearch}`;
      const { output } = await langbase.agent.run({
        model: 'perplexity:sonar-pro', apiKey: process.env.PERPLEXITY_API_KEY!,
        instructions: 'Produce supplier recommendations, cost analysis, risks, compliance, timeline and next steps.',
        input: [{ role: 'user', content: `Generate procurement recommendations from: ${context}` }], stream: false,
      });
      return output;
    }});

    const documents = await step({ id: 'documents', run: async () => {
      const { output } = await langbase.agent.run({
        model: 'perplexity:sonar-pro', apiKey: process.env.PERPLEXITY_API_KEY!,
        instructions: 'Generate appropriate RFQ/RFP/PO drafts based on recommendations.',
        input: [{ role: 'user', content: `Generate docs for: ${input}\n\nRecommendations: ${recommendations}` }], stream: false,
      });
      return output;
    }});

    const actionPlan = await step({ id: 'action_plan', run: async () => {
      const { output } = await langbase.agent.run({
        model: 'perplexity:sonar-pro', apiKey: process.env.PERPLEXITY_API_KEY!,
        instructions: 'Create an actionable procurement plan with steps, budget, risks, compliance and KPIs.',
        input: [{ role: 'user', content: `Create plan for: ${input}\nRecommendations: ${recommendations}\nDocuments: ${documents}` }], stream: false,
      });
      return output;
    }});

    const result = {
      status: 'success',
      source: 'local',
      query_analysis: queryAnalysis,
      market_intelligence: marketResearch,
      recommendations,
      documents,
      action_plan: actionPlan,
      timestamp: new Date().toISOString(),
    };
    await workflow.end();
    return { ok: true, status: 200, data: result };
  } catch (e) {
    await workflow.end();
    const message = e instanceof Error ? e.message : String(e);
    return { ok: false, status: 500, data: { status: 'error', message } };
  }
}

async function handleAgentRequest(request: Request) {
  try {
    const { input } = await request.json();

    if (!input || typeof input !== 'string') {
      return new Response(JSON.stringify({ error: "Input is required and must be a string" }), { status: 400, headers: { "Content-Type": "application/json" } });
    }

    const mode = (process.env.AGENT_MODE || 'hosted').toLowerCase();

    if (mode === 'local') {
      const local = await runLocalWorkflow(input);
      return new Response(JSON.stringify(local.data), { status: local.status, headers: { 'Content-Type': 'application/json' } });
    }

    // Hosted-first, fallback to local on failure or degraded output
    const hosted = await runHosted(input);
    if (!hosted.ok || isDegraded(hosted.data)) {
      const local = await runLocalWorkflow(input);
      const payload = { fallback_from: 'hosted', hosted_status: hosted.status, hosted_preview: hosted.data, ...local.data };
      return new Response(JSON.stringify(payload), { status: local.status, headers: { 'Content-Type': 'application/json' } });
    }

    // Phase 2: try to keep structured result shape if hosted provides it
    return new Response(JSON.stringify({ source: 'hosted', ...hosted.data }), { status: hosted.status, headers: { 'Content-Type': 'application/json' } });

  } catch (error) {
    console.error('Error in handleAgentRequest:', error);
    const message = error instanceof Error ? error.message : String(error);
    return new Response(JSON.stringify({ error: message || 'Internal server error' }), { status: 500, headers: { "Content-Type": "application/json" } });
  }
}