import dotenv from "dotenv";
dotenv.config();
import { Langbase } from "langbase";

async function procurementAgentWorkflow({ input, env }) {
  const langbase = new Langbase({
    apiKey: process.env.LANGBASE_API_KEY!,
    apiUrl: process.env.LANGBASE_API_URL,
  });

  const workflow = langbase.workflow();
  const { step } = workflow;
  const errors: string[] = [];

  try {
    // Step 1: Analyze user query and determine procurement intent
    const queryAnalysis = await step({
      id: "analyze_query",
      run: async () => {
        const { output } = await langbase.agent.run({
          model: "perplexity:sonar-pro",
          apiKey: process.env.PERPLEXITY_API_KEY!,
          instructions: `You are a Procurement AI Agent. Analyze the user query and determine:
          1. Type of procurement request (supplier search, RFQ generation, invoice processing, etc.)
          2. Key requirements and constraints
          3. Budget considerations
          4. Regional preferences (especially for Mangaluru, India)
          5. Compliance requirements
          
          Respond with a structured analysis including the procurement type and key parameters.`,
          input: [
            { role: "user", content: input },
          ],
          stream: false,
        });
        return output;
      },
    });

    // Step 2: Retrieve relevant procurement policies and supplier data
    const relevantMemories = await step({
      id: "retrieve_procurement_data",
      run: async () => {
        try {
          return await langbase.memories.retrieve({
            query: input,
            memory: [
              { name: "procurement-policies-1753937590380" },
              { name: "supplier-database-2rhhmmzx" },
              { name: "contract-templates-97ndpm2m" },
              { name: "compliance-docs-xiauwdun" }
            ],
          });
        } catch (error) {
          console.log("Memory retrieval failed, using fallback data");
          return [{
            text: "Fallback procurement policies: Standard approval workflows, ESG compliance required, regional supplier preferences for Mangaluru market."
          }];
        }
      },
    });

    // Step 3: Search for real-time market data and supplier information
    const marketResearch = await step({
      id: "market_research",
      run: async () => {
        const { output } = await langbase.agent.run({
          model: "perplexity:sonar-pro",
          apiKey: process.env.PERPLEXITY_API_KEY!,
          instructions: `Search for current market information related to the procurement request. Include:
          1. Current market prices and trends
          2. Available suppliers (especially in Mangaluru region if relevant)
          3. Compliance and certification requirements
          4. Sustainability and ESG factors
          5. Recent industry developments
          
          Provide comprehensive market intelligence for informed procurement decisions.`,
          input: [
            { role: "user", content: `Research market data for: ${input}` },
          ],
          stream: false,
        });
        return output;
      },
    });

    // Step 4: Generate procurement recommendations
    const recommendations = await step({
      id: "generate_recommendations",
      run: async () => {
        const context = `
        Query Analysis: ${queryAnalysis}
        
        Internal Knowledge: ${relevantMemories.map(m => m.text).join('\n')}
        
        Market Research: ${marketResearch}
        `;

        const { output } = await langbase.agent.run({
          model: "perplexity:sonar-pro",
          apiKey: process.env.PERPLEXITY_API_KEY!,
          instructions: `You are an expert Procurement AI Agent. Based on the analysis and research, provide:

          1. **Supplier Recommendations**: Top 3-5 suppliers with rationale
          2. **Cost Analysis**: Budget estimates and cost-saving opportunities
          3. **Risk Assessment**: Potential risks and mitigation strategies
          4. **Compliance Check**: Regulatory and ESG compliance status
          5. **Timeline**: Recommended procurement timeline
          6. **Next Steps**: Specific actions to take (RFQ, RFP, direct purchase, etc.)
          7. **Regional Considerations**: Special considerations for Mangaluru/India market
          8. **Approval Workflow**: Required approvals based on budget and policies

          Format the response as a comprehensive procurement recommendation report.`,
          input: [
            { role: "user", content: `Generate procurement recommendations based on this context: ${context}` },
          ],
          stream: false,
        });
        return output;
      },
    });

    // Step 5: Generate actionable documents (RFQ, RFP, or PO draft)
    const documentGeneration = await step({
      id: "generate_documents",
      run: async () => {
        const { output } = await langbase.agent.run({
          model: "perplexity:sonar-pro",
          apiKey: process.env.PERPLEXITY_API_KEY!,
          instructions: `Based on the procurement recommendations, generate appropriate procurement documents:

          1. If supplier search: Create RFQ (Request for Quotation) template
          2. If complex procurement: Create RFP (Request for Proposal) outline
          3. If direct purchase: Create PO (Purchase Order) draft
          4. Include all necessary terms, conditions, and specifications
          5. Ensure compliance with company policies and regional regulations
          6. Add ESG and sustainability requirements where applicable

          Format as ready-to-use procurement documents.`,
          input: [
            { role: "user", content: `Generate procurement documents for: ${input}\n\nBased on recommendations: ${recommendations}` },
          ],
          stream: false,
        });
        return output;
      },
    });

    // Step 6: Create final procurement action plan
    const actionPlan = await step({
      id: "create_action_plan",
      run: async () => {
        const { output } = await langbase.agent.run({
          model: "perplexity:sonar-pro",
          apiKey: process.env.PERPLEXITY_API_KEY!,
          instructions: `Create a comprehensive procurement action plan that includes:

          1. **Executive Summary**: Key findings and recommendations
          2. **Immediate Actions**: What to do next (within 24-48 hours)
          3. **Short-term Actions**: Steps for the next 1-2 weeks
          4. **Long-term Strategy**: Ongoing procurement optimization
          5. **Budget Impact**: Financial implications and savings opportunities
          6. **Risk Mitigation**: How to address identified risks
          7. **Compliance Checklist**: Ensure all regulatory requirements are met
          8. **Performance Metrics**: KPIs to track procurement success
          9. **Stakeholder Communication**: Who needs to be informed/involved
          10. **Regional Adaptations**: Specific considerations for local market

          Make it actionable and specific to the user's procurement needs.`,
          input: [
            { role: "user", content: `Create action plan for procurement request: ${input}

            Recommendations: ${recommendations}
            Documents: ${documentGeneration}` },
          ],
          stream: false,
        });
        return output;
      },
    });

    // Compile final response
    const finalResponse = {
      status: "success",
      query_analysis: queryAnalysis,
      market_intelligence: marketResearch,
      recommendations: recommendations,
      documents: documentGeneration,
      action_plan: actionPlan,
      timestamp: new Date().toISOString(),
      region: "Mangaluru, India (with global considerations)"
    };

    return finalResponse;

  } catch (err) {
    console.error("Procurement workflow error:", err);
    
    // Return a fallback response instead of throwing
    return {
      status: "error",
      message: "Procurement analysis completed with limited data",
      fallback_response: `I've analyzed your procurement request: "${input}". 
      
      Based on general procurement best practices:
      1. Verify supplier credentials and ESG compliance
      2. Compare at least 3 quotes for purchases over $5,000
      3. Consider local Mangaluru suppliers for regional advantages
      4. Ensure all documentation meets compliance requirements
      5. Follow standard approval workflows
      
      Please check your API keys and memory setup for full functionality.`,
      timestamp: new Date().toISOString()
    };
  } finally {
    await workflow.end();
  }
}

async function main(event, env) {
  try {
    const { input } = await event.json();
    const result = await procurementAgentWorkflow({ input, env });
    return result;
  } catch (error) {
    return {
      status: "error",
      message: "Failed to process procurement request",
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

export default main;

(async () => {
  const event = {
    json: async () => ({
      input: 'Your input goes here.',
    }),
  };
  const result = await main(event, {});
  console.log(result);
})();